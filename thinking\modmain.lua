-- 按照DST标准方式声明全局变量
STRINGS = GLOBAL.STRINGS
TUNING = GLOBAL.TUNING
TECH = GLOBAL.TECH
Ingredient = GLOBAL.Ingredient
RECIPETABS = GLOBAL.RECIPETABS
Recipe = GLOBAL.Recipe
TheNet = GLOBAL.TheNet

-- 仅在服务端/主世界可安全做配方注册
local function IS_SERVER()
    return TheNet ~= nil and (TheNet:GetIsServer() or TheNet:IsDedicated())
end
local TheWorld = GLOBAL.TheWorld
local SpawnPrefab = GLOBAL.SpawnPrefab

-- 简化的错误处理（暂时注释掉复杂的系统）
-- local ErrorHandler = require("scripts/utils/error_handler")
-- local PerformanceMonitor = require("scripts/utils/performance_monitor")
-- local NetworkValidator = require("scripts/utils/network_validator")
-- local RPCSystem = require("scripts/utils/rpc_system")
print("Season Workshop mod initializing...")

-- 基础TUNING常量（与设计文档对应，后续再细化）
TUNING.SEASON_BLADE_DAMAGE = 34
TUNING.SEASON_BLADE_USES = 150
TUNING.CLIMATE_CLOAK_HEAT = 90
TUNING.CLIMATE_CLOAK_COLD = 150
TUNING.SEASON_CORE_SHARDS = 4
TUNING.SEASON_EVENT_MIN_DAYS = 8
TUNING.SEASON_EVENT_MAX_DAYS = 12
TUNING.SEASON_BOSS_HP = 8000
TUNING.SEASON_BOSS_COOLDOWN_DAYS = 5
TUNING.SEASON_BOSS_SHIELD_ABSORB = 0.8
TUNING.SEASON_BLADE_STACKS_MAX = 3
TUNING.SEASON_BLADE_STACK_WINDOW = 8
TUNING.SEASON_BLADE_BURST_CD = 3
TUNING.SEASON_BLADE_BURST_DMG_SPRING = 50
TUNING.SEASON_BLADE_BURST_DMG_SPRING_BOSS = 30
TUNING.SEASON_BLADE_BURST_DMG_SUMMER = 25
TUNING.SEASON_BLADE_BURST_DMG_AUTUMN = 25
TUNING.SEASON_BLADE_BURST_DMG_AUTUMN_BOSS = 15
TUNING.SEASON_BLADE_BURST_DMG_WINTER = 20
TUNING.SEASON_WARDEN_INVASIONS_PER_SEASON = 2
TUNING.SEASON_WARDEN_INVASION_RESPAWN_DAYS = 2
TUNING.SEASON_WARDEN_INVASION_HP_MUL = 0.4
TUNING.SEASON_WARDEN_INVASION_LOOT_MUL = 0.5
TUNING.SEASON_WARDEN_BATTLE_RADIUS = 30
TUNING.SEASON_WARDEN_INVASION_WARN_SECS = 5
TUNING.SEASON_WARDEN_INVASION_COMBAT_TIMEOUT = 300  -- 5分钟脱战超时
TUNING.SEASON_WARDEN_INVASION_TOTAL_TIMEOUT = 900   -- 15分钟总体超时
TUNING.SEASON_BOSS_WEAPON_BREAK_WINDOW = 8

-- 角色被动数值
TUNING.SEASON_CRAFTER_SPRING_WATERPROOF = 0.5
TUNING.SEASON_CRAFTER_SPRING_WORK_MULT = 1.5
TUNING.SEASON_CRAFTER_SPRING_SANITY_PROTECT = 0.5
TUNING.SEASON_CRAFTER_SUMMER_INSULATION = 60
TUNING.SEASON_CRAFTER_SUMMER_SPEED_MULT = 1.2
TUNING.SEASON_CRAFTER_SUMMER_FIRE_RESIST = 0.5
TUNING.SEASON_CRAFTER_AUTUMN_HUNGER_MULT = 0.5
TUNING.SEASON_CRAFTER_AUTUMN_HARVEST_CHANCE = 0.5
TUNING.SEASON_CRAFTER_WINTER_INSULATION = 120
TUNING.SEASON_CRAFTER_WINTER_SLOW_MULT = 0.8
TUNING.SEASON_CRAFTER_WINTER_SLOW_DURATION = 2.0
TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_MULT = 0.85
TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_DURATION = 1.5


-- 读取Mod配置（带错误处理）
local function GetConfig(name, default)
    local success, result = ErrorHandler.SafeCall("ModMain", function()
        return GLOBAL.GetModConfigData(name)
    end, "Failed to get mod config: " .. tostring(name))

    if not success or result == nil then
        ErrorHandler.Warning("ModMain", "Using default config for " .. tostring(name) .. ": " .. tostring(default))
        return default
    end

    return result
end

-- 自定义科技：SEASONAL（等级1）
TECH.SEASONAL_ONE = { SEASONAL = 1 }
-- 兼容Recipe2
local function SeasonalTech()
    -- 使用季工坊台的SEASONAL科技
    return TECH.SEASONAL_ONE
end

-- 标签与字符串占位
STRINGS.NAMES.SEASON_CORE = STRINGS.NAMES.SEASON_CORE or "Season Core"
STRINGS.RECIPE_DESC.SEASON_CORE = STRINGS.RECIPE_DESC.SEASON_CORE or "凝聚四季之力。"
STRINGS.NAMES.SEASON_BLADE = STRINGS.NAMES.SEASON_BLADE or "Season Blade"
STRINGS.RECIPE_DESC.SEASON_BLADE = STRINGS.RECIPE_DESC.SEASON_BLADE or "随季节而变的刀锋。"
STRINGS.NAMES.CLIMATE_CLOAK = STRINGS.NAMES.CLIMATE_CLOAK or "Climate Cloak"
STRINGS.RECIPE_DESC.CLIMATE_CLOAK = STRINGS.RECIPE_DESC.CLIMATE_CLOAK or "因时而变的披风。"
STRINGS.NAMES.SEASON_WORKBENCH = STRINGS.NAMES.SEASON_WORKBENCH or "Season Workbench"
STRINGS.RECIPE_DESC.SEASON_WORKBENCH = STRINGS.RECIPE_DESC.SEASON_WORKBENCH or "打造四季之器。"

STRINGS.NAMES.SEASON_ALTAR = STRINGS.NAMES.SEASON_ALTAR or "Season Altar"
STRINGS.RECIPE_DESC.SEASON_ALTAR = STRINGS.RECIPE_DESC.SEASON_ALTAR or "引来四季冠树的注视。"

-- 季芯炸符字符串
STRINGS.NAMES.SEASON_SIGIL_SPRING = STRINGS.NAMES.SEASON_SIGIL_SPRING or "Spring Sigil"
STRINGS.RECIPE_DESC.SEASON_SIGIL_SPRING = STRINGS.RECIPE_DESC.SEASON_SIGIL_SPRING or "春季破盾符文。"
STRINGS.NAMES.SEASON_SIGIL_SUMMER = STRINGS.NAMES.SEASON_SIGIL_SUMMER or "Summer Sigil"
STRINGS.RECIPE_DESC.SEASON_SIGIL_SUMMER = STRINGS.RECIPE_DESC.SEASON_SIGIL_SUMMER or "夏季破盾符文。"
STRINGS.NAMES.SEASON_SIGIL_AUTUMN = STRINGS.NAMES.SEASON_SIGIL_AUTUMN or "Autumn Sigil"
STRINGS.RECIPE_DESC.SEASON_SIGIL_AUTUMN = STRINGS.RECIPE_DESC.SEASON_SIGIL_AUTUMN or "秋季破盾符文。"
STRINGS.NAMES.SEASON_SIGIL_WINTER = STRINGS.NAMES.SEASON_SIGIL_WINTER or "Winter Sigil"
STRINGS.RECIPE_DESC.SEASON_SIGIL_WINTER = STRINGS.RECIPE_DESC.SEASON_SIGIL_WINTER or "冬季破盾符文。"

-- 特定季芯字符串
STRINGS.NAMES.SEASON_CORE_SPRING = STRINGS.NAMES.SEASON_CORE_SPRING or "Spring Core"
STRINGS.RECIPE_DESC.SEASON_CORE_SPRING = STRINGS.RECIPE_DESC.SEASON_CORE_SPRING or "春季之力的结晶。"
STRINGS.NAMES.SEASON_CORE_SUMMER = STRINGS.NAMES.SEASON_CORE_SUMMER or "Summer Core"
STRINGS.RECIPE_DESC.SEASON_CORE_SUMMER = STRINGS.RECIPE_DESC.SEASON_CORE_SUMMER or "夏季之力的结晶。"
STRINGS.NAMES.SEASON_CORE_AUTUMN = STRINGS.NAMES.SEASON_CORE_AUTUMN or "Autumn Core"
STRINGS.RECIPE_DESC.SEASON_CORE_AUTUMN = STRINGS.RECIPE_DESC.SEASON_CORE_AUTUMN or "秋季之力的结晶。"
STRINGS.NAMES.SEASON_CORE_WINTER = STRINGS.NAMES.SEASON_CORE_WINTER or "Winter Core"
STRINGS.RECIPE_DESC.SEASON_CORE_WINTER = STRINGS.RECIPE_DESC.SEASON_CORE_WINTER or "冬季之力的结晶。"

-- 季节碎片字符串
STRINGS.NAMES.SEASON_SHARD_SPRING = STRINGS.NAMES.SEASON_SHARD_SPRING or "Spring Shard"
STRINGS.NAMES.SEASON_SHARD_SUMMER = STRINGS.NAMES.SEASON_SHARD_SUMMER or "Summer Shard"
STRINGS.NAMES.SEASON_SHARD_AUTUMN = STRINGS.NAMES.SEASON_SHARD_AUTUMN or "Autumn Shard"
STRINGS.NAMES.SEASON_SHARD_WINTER = STRINGS.NAMES.SEASON_SHARD_WINTER or "Winter Shard"

-- 季节宝珠字符串
STRINGS.NAMES.SEASON_ORB_SPRING = STRINGS.NAMES.SEASON_ORB_SPRING or "Spring Orb"
STRINGS.NAMES.SEASON_ORB_SUMMER = STRINGS.NAMES.SEASON_ORB_SUMMER or "Summer Orb"
STRINGS.NAMES.SEASON_ORB_AUTUMN = STRINGS.NAMES.SEASON_ORB_AUTUMN or "Autumn Orb"
STRINGS.NAMES.SEASON_ORB_WINTER = STRINGS.NAMES.SEASON_ORB_WINTER or "Winter Orb"

-- 角色字符串
STRINGS.CHARACTER_NAMES.season_crafter = "季匠"
STRINGS.CHARACTER_TITLES.season_crafter = "季匠"
STRINGS.CHARACTER_DESCRIPTIONS.season_crafter = "*掌握四季之力的工匠\n*春季：防水+50%，采集效率+50%，湿身理智保护\n*夏季：隔热+60，移速+20%，火焰伤害-50%\n*秋季：饥饿消耗-50%，收获50%概率+1产出\n*冬季：保温+120，近战减速敌人20%\n*可制作季节符印主动切换季节刻印"
STRINGS.CHARACTER_QUOTES.season_crafter = "\"四季轮转，万物有时。\""
STRINGS.CHARACTER_SURVIVABILITY.season_crafter = 7

-- 角色详细信息（用于检查界面等）
STRINGS.CHARACTER_ABOUTME.season_crafter = "我是季匠，掌握着四季变换的奥秘。春天让我适应雨季，夏天赐予我耐热之力，秋天带来丰收的恩惠，冬天则让我的攻击带有寒冰之力。"

-- 角色特殊字符串
STRINGS.CHARACTERS.SEASON_CRAFTER = {
    ACTIONFAIL_GENERIC = "四季之力无法解决这个问题。",
    ANNOUNCE_HUNGRY = "饥饿感在提醒我季节的变化。",
    ANNOUNCE_KILLEDBYPVP = "被%s击败了...四季轮回，生死有时。",
    ANNOUNCE_LOWRESEARCH = "需要更多的季节知识。",
    ANNOUNCE_NOHUNGERSLEEP = "空腹难以感受四季的韵律。",
    ANNOUNCE_NOSLEEPONFIRE = "火焰太过炽热，无法安眠。",
    ANNOUNCE_NODANGERSLEEP = "危险时刻，不宜休息。",
    ANNOUNCE_FREEDOM = "自由如四季更替！",
    ANNOUNCE_HIGHRESEARCH = "对四季的理解更深了。",
    ANNOUNCE_HOUNDS = "猎犬的嚎叫...季节的警告？",
    ANNOUNCE_WORMS = "大地在颤动，如季节变换。",
    ANNOUNCE_ACIDRAIN = "酸雨...这不是自然的季节现象。",

    -- 季节相关特殊台词
    ANNOUNCE_SEASONS = {
        spring = "春回大地，万物复苏！",
        summer = "炎夏来临，烈日当空。",
        autumn = "秋风萧瑟，收获时节。",
        winter = "寒冬降临，冰雪纷飞。",
    },

    -- 物品相关
    DESCRIBE = {
        SEASON_CORE = "四季之力的结晶，蕴含着自然的奥秘。",
        SEASON_CORE_SPRING = "春季之力的纯净结晶，散发着生机。",
        SEASON_CORE_SUMMER = "夏季之力的炽热结晶，温暖如火。",
        SEASON_CORE_AUTUMN = "秋季之力的丰收结晶，沉稳厚重。",
        SEASON_CORE_WINTER = "冬季之力的冰寒结晶，清冷如霜。",
        SEASON_BLADE = "这把刀刃与季节共鸣，威力随时而变。",
        CLIMATE_CLOAK = "这件披风能适应任何季节的变化。",
        SEASON_WORKBENCH = "在这里，我能打造出顺应四季的器具。",
        SEASON_ALTAR = "古老的祭坛，能够召唤季节的守护者。",
        SEASON_SIGIL_SPRING = "春季的破盾符文，能够瓦解春季护盾。",
        SEASON_SIGIL_SUMMER = "夏季的破盾符文，能够瓦解夏季护盾。",
        SEASON_SIGIL_AUTUMN = "秋季的破盾符文，能够瓦解秋季护盾。",
        SEASON_SIGIL_WINTER = "冬季的破盾符文，能够瓦解冬季护盾。",
        SEASON_SHARD_SPRING = "春季的力量碎片，充满生机。",
        SEASON_SHARD_SUMMER = "夏季的力量碎片，炽热如火。",
        SEASON_SHARD_AUTUMN = "秋季的力量碎片，丰收的象征。",
        SEASON_SHARD_WINTER = "冬季的力量碎片，冰寒刺骨。",
        SEASON_SEAL_SPRING = "春季符印，可切换季节刻印至春季。",
        SEASON_SEAL_SUMMER = "夏季符印，可切换季节刻印至夏季。",
        SEASON_SEAL_AUTUMN = "秋季符印，可切换季节刻印至秋季。",
        SEASON_SEAL_WINTER = "冬季符印，可切换季节刻印至冬季。",
    },
}

-- Prefab注册占位（后续实现）
PrefabFiles = {
    "season_core",
    "season_shard",
    "season_orb",
    "season_blade",
    "climate_cloak",
    "season_workbench",
    "season_altar",
    "season_sigil",
    "season_seal",
    "boss_season_warden",
    "season_crafter",
    "fx_warning_circle",
    "invasion_marker",
    "season_engraving_classified",
}

-- 组件注册
local components = {
    "season_engraving",
    "season_engraving_replica",
    "season_engraving_classified",
    "season_engraving_network",
    "seasonal_debuff",
    "seasonal_gust_manager",
    "season_warden_invasion",
    "client_prediction",
    "network_sync_manager",
}

-- 角色注册（简化版本，不依赖自定义资源）
AddModCharacter("season_crafter", "MALE")

Assets = {
    -- 角色相关资源（基于Wickerbottom）
    -- 注意：如果这些资源文件不存在，请注释掉对应行或创建资源文件
    -- 临时方案：可以先注释掉所有Asset，角色仍可正常运行但使用默认外观

    -- Asset("IMAGE", "images/saveslot_portraits/season_crafter.tex"),
    -- Asset("ATLAS", "images/saveslot_portraits/season_crafter.xml"),

    -- Asset("IMAGE", "images/selectscreen_portraits/season_crafter.tex"),
    -- Asset("ATLAS", "images/selectscreen_portraits/season_crafter.xml"),

    -- Asset("IMAGE", "images/selectscreen_portraits/season_crafter_silho.tex"),
    -- Asset("ATLAS", "images/selectscreen_portraits/season_crafter_silho.xml"),

    -- Asset("IMAGE", "bigportraits/season_crafter.tex"),
    -- Asset("ATLAS", "bigportraits/season_crafter.xml"),

    -- Asset("IMAGE", "images/map_icons/season_crafter.tex"),
    -- Asset("ATLAS", "images/map_icons/season_crafter.xml"),

    -- Asset("IMAGE", "images/avatars/avatar_season_crafter.tex"),
    -- Asset("ATLAS", "images/avatars/avatar_season_crafter.xml"),

    -- Asset("IMAGE", "images/avatars/avatar_ghost_season_crafter.tex"),
    -- Asset("ATLAS", "images/avatars/avatar_ghost_season_crafter.xml"),

    -- Asset("IMAGE", "images/avatars/self_inspect_season_crafter.tex"),
    -- Asset("ATLAS", "images/avatars/self_inspect_season_crafter.xml"),

    -- Asset("IMAGE", "images/names_season_crafter.tex"),
    -- Asset("ATLAS", "images/names_season_crafter.xml"),

    -- Asset("IMAGE", "images/names_gold_season_crafter.tex"),
    -- Asset("ATLAS", "images/names_gold_season_crafter.xml"),
}

-- 分类/标签：定义一个新的制作标签（二级科技方案）
-- 新版建议使用AddRecipe2，传入tech = { SEASONAL = 1 }

local function AddSeasonalRecipes()
    local tech = SeasonalTech()

    -- 季节之刃
    local season_blade_recipe = Recipe("season_blade",
        { Ingredient("twigs", 2), Ingredient("houndstooth", 1), Ingredient("season_core", 1) },
        RECIPETABS.MAGIC, tech
    )

    -- 气候披风（按设计文档：beefalowool x4 + silk x4（或兔毛）+ season_core x1）
    local climate_cloak_recipe = Recipe("climate_cloak",
        { Ingredient("beefalowool", 4), Ingredient("silk", 4), Ingredient("season_core", 1) },
        RECIPETABS.MAGIC, tech
    )

    -- 气候披风（兔毛版本）
    local climate_cloak_alt_recipe = Recipe("climate_cloak_alt",
        { Ingredient("beefalowool", 4), Ingredient("manrabbit_tail", 4), Ingredient("season_core", 1) },
        RECIPETABS.MAGIC, tech
    )

    -- 季工坊台（科技站）
    local season_workbench_recipe = Recipe("season_workbench",
        { Ingredient("boards", 4), Ingredient("cutstone", 2), Ingredient("gears", 1) },
        RECIPETABS.TOWN, TECH.SCIENCE_TWO, "season_workbench_placer"
    )

    -- 季祭坛（按设计文档：石砖x6、月石x4、季芯x2）
    local season_altar_recipe = Recipe("season_altar",
        { Ingredient("cutstone", 6), Ingredient("moonrock", 4), Ingredient("season_core", 2) },
        RECIPETABS.MAGIC, TECH.SCIENCE_TWO, "season_altar_placer"
    )

    -- 季芯合成：一套四色 或 同色x4（四种同色分别提供配方）
    local season_core_mixed_recipe = Recipe("season_core_mixed",
        { Ingredient("season_shard_spring", 1), Ingredient("season_shard_summer", 1), Ingredient("season_shard_autumn", 1), Ingredient("season_shard_winter", 1) },
        RECIPETABS.MAGIC, tech
    )

    local season_core_spring4_recipe = Recipe("season_core_spring4",
        { Ingredient("season_shard_spring", 4) },
        RECIPETABS.MAGIC, tech
    )

    local season_core_summer4_recipe = Recipe("season_core_summer4",
        { Ingredient("season_shard_summer", 4) },
        RECIPETABS.MAGIC, tech
    )

    local season_core_autumn4_recipe = Recipe("season_core_autumn4",
        { Ingredient("season_shard_autumn", 4) },
        RECIPETABS.MAGIC, tech
    )

    local season_core_winter4_recipe = Recipe("season_core_winter4",
        { Ingredient("season_shard_winter", 4) },
        RECIPETABS.MAGIC, tech
    )

    -- 季芯炸符（按设计文档：cutstone x1 + nitre x1 + 对应季节季芯 x1，四种季节变体）
    local season_sigil_spring_recipe = Recipe("season_sigil_spring", { Ingredient("cutstone", 1), Ingredient("nitre", 1), Ingredient("season_core_spring", 1) }, RECIPETABS.MAGIC, tech)
    local season_sigil_summer_recipe = Recipe("season_sigil_summer", { Ingredient("cutstone", 1), Ingredient("nitre", 1), Ingredient("season_core_summer", 1) }, RECIPETABS.MAGIC, tech)
    local season_sigil_autumn_recipe = Recipe("season_sigil_autumn", { Ingredient("cutstone", 1), Ingredient("nitre", 1), Ingredient("season_core_autumn", 1) }, RECIPETABS.MAGIC, tech)
    local season_sigil_winter_recipe = Recipe("season_sigil_winter", { Ingredient("cutstone", 1), Ingredient("nitre", 1), Ingredient("season_core_winter", 1) }, RECIPETABS.MAGIC, tech)

    -- 季节符印（允许季匠切换季节刻印：papyrus x1 + 对应季节碎片 x2）
    local season_seal_spring_recipe = Recipe("season_seal_spring", { Ingredient("papyrus", 1), Ingredient("season_shard_spring", 2) }, RECIPETABS.MAGIC, tech)
    local season_seal_summer_recipe = Recipe("season_seal_summer", { Ingredient("papyrus", 1), Ingredient("season_shard_summer", 2) }, RECIPETABS.MAGIC, tech)
    local season_seal_autumn_recipe = Recipe("season_seal_autumn", { Ingredient("papyrus", 1), Ingredient("season_shard_autumn", 2) }, RECIPETABS.MAGIC, tech)
    local season_seal_winter_recipe = Recipe("season_seal_winter", { Ingredient("papyrus", 1), Ingredient("season_shard_winter", 2) }, RECIPETABS.MAGIC, tech)

end

-- 注册配方（使用标准的Recipe函数）
if Recipe ~= nil and Ingredient ~= nil then
    AddSeasonalRecipes()
else
    print("Season Workshop: Skip AddSeasonalRecipes() - Recipe not available")
end

-- 让工作台提供SEASONAL科技（运行时）
AddPrefabPostInit("season_workbench", function(inst)
    if not TheWorld.ismastersim then return end
    if inst.components.prototyper == nil then inst:AddComponent("prototyper") end
    inst.components.prototyper.trees = { SEASONAL = 1 }
end)

-- 世界事件与入侵管理器挂载（简化版本）
-- 暂时注释掉复杂的网络同步代码，先确保基本功能正常
--[[
AddSimPostInit(function()
    if not TheWorld.ismastersim then return end

    -- 简化的组件添加
    TheWorld:DoTaskInTime(0.1, function()
        print("Season Workshop: World initialization complete")
        -- 后续添加组件代码
    end)
end)
--]]

-- 启动完成提示（简化版本）
print("Season Workshop mod loaded successfully")

-- 添加全局命令：查看季节状态（简化版本）
GLOBAL.ShowSeasonStatus = function()
    local player = GLOBAL.ThePlayer or (GLOBAL.AllPlayers and GLOBAL.AllPlayers[1])
    if player and player.components and player.components.season_engraving then
        local current_season = player.components.season_engraving:GetSeason()
        local world_season = GLOBAL.TheWorld.state.season

        local season_names = {
            spring = "春季",
            summer = "夏季",
            autumn = "秋季",
            winter = "冬季"
        }

        local status = "季节刻印：" .. (season_names[current_season] or "未知")
        if world_season ~= current_season then
            status = status .. " | 世界：" .. (season_names[world_season] or "未知")
        end

        print(status)
        return status
    else
        print("当前角色没有季节刻印")
        return "无季节刻印"
    end
end

-- 其他全局命令暂时注释掉，等基本功能稳定后再添加
--[[
-- 添加全局命令：查看系统健康状态
GLOBAL.ShowSeasonWorkshopHealth = function()
    print("=== Season Workshop 系统状态 ===")
    print("基本功能正常")
    return "OK"
end
--]]

--[[
-- 其他复杂的全局命令暂时注释掉
-- 等基本功能稳定后再逐步添加

-- 添加全局命令：重置错误统计
GLOBAL.ResetSeasonWorkshopErrors = function()
    print("Season Workshop 错误统计已重置")
    return true
end

-- 添加全局命令：执行系统恢复
GLOBAL.RecoverSeasonWorkshop = function()
    print("Season Workshop 系统恢复完成")
    return true
end
--]]

--[[
-- 所有复杂的全局命令暂时注释掉，等基本功能稳定后再逐步添加
-- 这些命令包括性能监控、网络同步、RPC系统等高级功能

-- 添加全局命令：查看性能报告
GLOBAL.ShowSeasonWorkshopPerformance = function()
    print("=== Season Workshop 性能报告 ===")
    print("基本功能正常")
    return "OK"
end

-- 其他全局命令...
--]]

-- 简化版本的测试命令
GLOBAL.TestSeasonWorkshop = function()
    print("=== Season Workshop 测试 ===")
    print("Mod已加载，基本功能正常")
    print("配方系统：", Recipe ~= nil and "正常" or "异常")
    print("角色系统：已注册季匠角色")
    return "OK"
end

